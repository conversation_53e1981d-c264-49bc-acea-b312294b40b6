import { describe, it, expect, vi, beforeEach } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { render, screen } from '@testing-library/svelte';
import CharacterAddForm from './CharacterAddForm.svelte';

vi.mock('$/lib/createTexture', () => ({
  createTextureFromFile: vi.fn(async (file: File) => ({
    url: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB',
    texture: { width: 100, height: 200 },
  })),
  createTextureFromUrl: vi.fn(async (url: string) => ({ width: 100, height: 200 })),
}));

declare module 'vitest' {
  export interface ProvidedContext {}
}

const createFile = (name = 'avatar.png', type = 'image/png') => new File([new Uint8Array([1, 2, 3])], name, { type });

beforeEach(() => {
  vi.restoreAllMocks();
});

describe('CharacterAddForm.svelte', () => {
  it('shows Add a character initially and becomes Replace image after upload', async () => {
    const onCreate = vi.fn();
    const { container } = render(CharacterAddForm, { onCreate });

    // Initially shows Add a character button
    expect(screen.getByRole('button', { name: "Add a character" })).toBeInTheDocument();

    const input = container.querySelector('input[type="file"]') as HTMLInputElement;
    const file = createFile();
    Object.defineProperty(input, 'files', {
      configurable: true,
      value: { 0: file, length: 1, item: (i: number) => file },
    });
    input.dispatchEvent(new Event('change'));

    // After upload, onCreate is called and button label changes
    expect(await screen.findByRole('button', { name: "Replace image" })).toBeInTheDocument();
    expect(onCreate).toHaveBeenCalledTimes(1);

    // Image preview should be visible
    expect(screen.getByRole('img')).toBeInTheDocument();
  });

  it('submits form and calls backend', async () => {
    const onSubmit = vi.fn();
    const { container } = render(CharacterAddForm, { onSubmit });

    const input = container.querySelector('input[type="file"]') as HTMLInputElement;
    const file = createFile();
    Object.defineProperty(input, 'files', {
      configurable: true,
      value: { 0: file, length: 1, item: (i: number) => file },
    });
    input.dispatchEvent(new Event('change'));

    const fetchMock = vi.fn(async () => new Response(JSON.stringify({ id: 7, imageUrl: 'https://example.com/img.png' }), { status: 200 }));
    vi.stubGlobal('fetch', fetchMock);

    // Click Submit
    const submitButton = await screen.findByRole('button', { name: "Submit" });
    submitButton.click();

    // Backend called with PUT
    await vi.waitFor(() => {
      expect(fetchMock).toHaveBeenCalled();
    });
    const [url, init] = fetchMock.mock.calls[0];
    expect(url).toBe('/api/character');
    expect((init as RequestInit).method).toBe('PUT');

    // onSubmit called
    await vi.waitFor(() => expect(onSubmit).toHaveBeenCalled());
  });
});

