import { vi } from 'vitest';

// Provide a minimal HTMLCanvasElement.getContext in jsdom so Three/<PERSON><PERSON><PERSON><PERSON> calls don't throw.
if (typeof HTMLCanvasElement !== 'undefined') {
  const proto: any = HTMLCanvasElement.prototype as any;
  const originalGetContext = proto.getContext;

  const glStub = {
    canvas: {},
    // minimal API surface that Three may poke at
    getExtension: vi.fn(),
    clearColor: vi.fn(),
    clear: vi.fn(),
    enable: vi.fn(),
    disable: vi.fn(),
    createShader: vi.fn(),
    shaderSource: vi.fn(),
    compileShader: vi.fn(),
    createProgram: vi.fn(),
    attachShader: vi.fn(),
    linkProgram: vi.fn(),
    useProgram: vi.fn(),
    getShaderParameter: vi.fn(),
    getProgramParameter: vi.fn(),
    getShaderInfoLog: vi.fn(),
    getProgramInfoLog: vi.fn(),
    createBuffer: vi.fn(),
    bindBuffer: vi.fn(),
    bufferData: vi.fn(),
    drawArrays: vi.fn(),
    viewport: vi.fn(),
  } as any;

  proto.getContext = vi.fn(function (this: any, type: string, ...args: any[]) {
    if (type === 'webgl' || type === 'webgl2') return glStub;
    if (originalGetContext) return originalGetContext.call(this, type, ...args);
    return null;
  });
}

