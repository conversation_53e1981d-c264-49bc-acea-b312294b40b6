{"version": "5", "specifiers": {"npm:@playwright/test@^1.54.2": "1.54.2", "npm:@supabase/supabase-js@^2.54.0": "2.54.0", "npm:@sveltejs/adapter-auto@^6.0.2": "6.0.2_@sveltejs+kit@2.27.3__@sveltejs+vite-plugin-svelte@6.1.1___svelte@5.38.0____acorn@8.15.0___vite@7.1.1____sass@1.90.0____picomatch@4.0.3___sass@1.90.0__svelte@5.38.0___acorn@8.15.0__vite@7.1.1___sass@1.90.0___picomatch@4.0.3__acorn@8.15.0__sass@1.90.0_@sveltejs+vite-plugin-svelte@6.1.1__svelte@5.38.0___acorn@8.15.0__vite@7.1.1___sass@1.90.0___picomatch@4.0.3__sass@1.90.0_svelte@5.38.0__acorn@8.15.0_vite@7.1.1__sass@1.90.0__picomatch@4.0.3_sass@1.90.0", "npm:@sveltejs/kit@^2.27.3": "2.27.3_@sveltejs+vite-plugin-svelte@6.1.1__svelte@5.38.0___acorn@8.15.0__vite@7.1.1___sass@1.90.0___picomatch@4.0.3__sass@1.90.0_svelte@5.38.0__acorn@8.15.0_vite@7.1.1__sass@1.90.0__picomatch@4.0.3_acorn@8.15.0_sass@1.90.0", "npm:@sveltejs/vite-plugin-svelte@^6.1.1": "6.1.1_svelte@5.38.0__acorn@8.15.0_vite@7.1.1__sass@1.90.0__picomatch@4.0.3_sass@1.90.0", "npm:@testing-library/jest-dom@^6.6.4": "6.6.4", "npm:@testing-library/svelte@^5.2.8": "5.2.8_svel<PERSON>@5.38.0__acorn@8.15.0_vite@7.1.1__sass@1.90.0__picomatch@4.0.3_vitest@3.2.4__jsdom@26.1.0__vite@7.1.1___sass@1.90.0___picomatch@4.0.3__sass@1.90.0_sass@1.90.0_jsdom@26.1.0", "npm:@threlte/core@^8.1.4": "8.1.4_svel<PERSON>@5.38.0__acorn@8.15.0_three@0.179.1", "npm:@threlte/extras@^9.4.4": "9.4.4_svelte@5.38.0__acorn@8.15.0_three@0.179.1_@types+three@0.179.0", "npm:@types/three@0.179": "0.179.0", "npm:@vaie/hui@^0.0.0": "0.0.0_svelte@5.38.0__acorn@8.15.0_sass@1.90.0_typescript@5.9.2", "npm:drizzle-kit@~0.31.4": "0.31.4_esbuild@0.25.8", "npm:drizzle-orm@~0.44.4": "0.44.4_postgres@3.4.7", "npm:jsdom@26": "26.1.0", "npm:postgres@^3.4.5": "3.4.7", "npm:sass@^1.90.0": "1.90.0", "npm:svelte-check@^4.3.1": "4.3.1_svel<PERSON>@5.38.0__acorn@8.15.0_typescript@5.9.2", "npm:svelte@^5.38.0": "5.38.0_acorn@8.15.0", "npm:three@~0.179.1": "0.179.1", "npm:typescript@^5.9.2": "5.9.2", "npm:vite@^7.1.1": "7.1.1_sass@1.90.0_picomatch@4.0.3", "npm:vitest@^3.2.4": "3.2.4_j<PERSON><PERSON>@26.1.0_vite@7.1.1__sass@1.90.0__picomatch@4.0.3_sass@1.90.0"}, "npm": {"@adobe/css-tools@4.4.3": {"integrity": "sha512-VQKMkwriZbaOgVCby1UDY/LDk5fIjhQicCvVPFqfe+69fWaPWydbWJ3wRt59/YzIwda1I81loas3oCoHxnqvdA=="}, "@ampproject/remapping@2.3.0": {"integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==", "dependencies": ["@jridgewell/gen-mapping", "@jridgewell/trace-mapping"]}, "@asamuzakjp/css-color@3.2.0_@csstools+css-parser-algorithms@3.0.5__@csstools+css-tokenizer@3.0.4_@csstools+css-tokenizer@3.0.4": {"integrity": "sha512-K1A6z8tS3XsmCMM86xoWdn7Fkdn9m6RSVtocUrJYIwZnFVkng/PvkEoWtOWmP+Scc6saYWHWZYbndEEXxl24jw==", "dependencies": ["@csstools/css-calc", "@csstools/css-color-parser", "@csstools/css-parser-algorithms", "@csstools/css-tokenizer", "lru-cache"]}, "@babel/code-frame@7.27.1": {"integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "dependencies": ["@babel/helper-validator-identifier", "js-tokens@4.0.0", "picocolors"]}, "@babel/helper-validator-identifier@7.27.1": {"integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/runtime@7.28.2": {"integrity": "sha512-KHp2IflsnGywDjBWDkR9iEqiWSpc8GIi0lgTT3mOElT0PP1tG26P4tmFI2YvAdzgq9RGyoHZQEIEdZy6Ec5xCA=="}, "@csstools/color-helpers@5.0.2": {"integrity": "sha512-JqWH1vsgdGcw2RR6VliXXdA0/59LttzlU8UlRT/iUUsEeWfYq8I+K0yhihEUTTHLRm1EXvpsCx3083EU15ecsA=="}, "@csstools/css-calc@2.1.4_@csstools+css-parser-algorithms@3.0.5__@csstools+css-tokenizer@3.0.4_@csstools+css-tokenizer@3.0.4": {"integrity": "sha512-3N8oaj+0juUw/1H3YwmDDJXCgTB1gKU6Hc/bB502u9zR0q2vd786XJH9QfrKIEgFlZmhZiq6epXl4rHqhzsIgQ==", "dependencies": ["@csstools/css-parser-algorithms", "@csstools/css-tokenizer"]}, "@csstools/css-color-parser@3.0.10_@csstools+css-parser-algorithms@3.0.5__@csstools+css-tokenizer@3.0.4_@csstools+css-tokenizer@3.0.4": {"integrity": "sha512-TiJ5Ajr6WRd1r8HSiwJvZBiJOqtH86aHpUjq5aEKWHiII2Qfjqd/HCWKPOW8EP4vcspXbHnXrwIDlu5savQipg==", "dependencies": ["@csstools/color-helpers", "@csstools/css-calc", "@csstools/css-parser-algorithms", "@csstools/css-tokenizer"]}, "@csstools/css-parser-algorithms@3.0.5_@csstools+css-tokenizer@3.0.4": {"integrity": "sha512-DaDeUkXZKjdGhgYaHNJTV9pV7Y9B3b644jCLs9Upc3VeNGg6LWARAT6O+Q+/COo+2gg/bM5rhpMAtf70WqfBdQ==", "dependencies": ["@csstools/css-tokenizer"]}, "@csstools/css-tokenizer@3.0.4": {"integrity": "sha512-Vd/9EVDiu6PPJt9yAh6roZP6El1xHrdvIVGjyBsHR0RYwNHgL7FJPyIIW4fANJNG6FtyZfvlRPpFI4ZM/lubvw=="}, "@dimforge/rapier3d-compat@0.12.0": {"integrity": "sha512-uekIGetywIgopfD97oDL5PfeezkFpNhwlzlaEYNOA0N6ghdsOvh/HYjSMek5Q2O1PYvRSDFcqFVJl4r4ZBwOow=="}, "@drizzle-team/brocli@0.10.2": {"integrity": "sha512-z33Il7l5dKjUgGULTqBsQBQwckHh5AbIuxhdsIxDDiZAzBOrZO6q9ogcWC65kU382AfynTfgNumVcNIjuIua6w=="}, "@esbuild-kit/core-utils@3.3.2": {"integrity": "sha512-sPRAnw9CdSsRmEtnsl2WXWdyquogVpB3yZ3dgwJfe8zrOzTsV7cJvmwrKVa+0ma5BoiGJ+BoqkMvawbayKUsqQ==", "dependencies": ["esbuild@0.18.20", "source-map-support"], "deprecated": true}, "@esbuild-kit/esm-loader@2.6.5": {"integrity": "sha512-FxEMIkJKnodyA1OaCUoEvbYRkoZlLZ4d/eXFu9Fh8CbBBgP5EmZxrfTRyN0qpXZ4vOvqnE5YdRdcrmUUXuU+dA==", "dependencies": ["@esbuild-kit/core-utils", "get-tsconfig"], "deprecated": true}, "@esbuild/aix-ppc64@0.25.8": {"integrity": "sha512-urAvrUedIqEiFR3FYSLTWQgLu5tb+m0qZw0NBEasUeo6wuqatkMDaRT+1uABiGXEu5vqgPd7FGE1BhsAIy9QVA==", "os": ["aix"], "cpu": ["ppc64"]}, "@esbuild/android-arm64@0.18.20": {"integrity": "sha512-Nz4rJcchGDtENV0eMKUNa6L12zz2zBDXuhj/Vjh18zGqB44Bi7MBMSXjgunJgjRhCmKOjnPuZp4Mb6OKqtMHLQ==", "os": ["android"], "cpu": ["arm64"]}, "@esbuild/android-arm64@0.25.8": {"integrity": "sha512-OD3p7LYzWpLhZEyATcTSJ67qB5D+20vbtr6vHlHWSQYhKtzUYrETuWThmzFpZtFsBIxRvhO07+UgVA9m0i/O1w==", "os": ["android"], "cpu": ["arm64"]}, "@esbuild/android-arm@0.18.20": {"integrity": "sha512-fyi7TDI/ijKKNZTUJAQqiG5T7YjJXgnzkURqmGj13C6dCqckZBLdl4h7bkhHt/t0WP+zO9/zwroDvANaOqO5Sw==", "os": ["android"], "cpu": ["arm"]}, "@esbuild/android-arm@0.25.8": {"integrity": "sha512-RONsAvGCz5oWyePVnLdZY/HHwA++nxYWIX1atInlaW6SEkwq6XkP3+cb825EUcRs5Vss/lGh/2YxAb5xqc07Uw==", "os": ["android"], "cpu": ["arm"]}, "@esbuild/android-x64@0.18.20": {"integrity": "sha512-8GDdlePJA8D6zlZYJV/jnrRAi6rOiNaCC/JclcXpB+KIuvfBN4owLtgzY2bsxnx666XjJx2kDPUmnTtR8qKQUg==", "os": ["android"], "cpu": ["x64"]}, "@esbuild/android-x64@0.25.8": {"integrity": "sha512-yJAVPklM5+4+9dTeKwHOaA+LQkmrKFX96BM0A/2zQrbS6ENCmxc4OVoBs5dPkCCak2roAD+jKCdnmOqKszPkjA==", "os": ["android"], "cpu": ["x64"]}, "@esbuild/darwin-arm64@0.18.20": {"integrity": "sha512-bxRHW5kHU38zS2lPTPOyuyTm+S+eobPUnTNkdJEfAddYgEcll4xkT8DB9d2008DtTbl7uJag2HuE5NZAZgnNEA==", "os": ["darwin"], "cpu": ["arm64"]}, "@esbuild/darwin-arm64@0.25.8": {"integrity": "sha512-Jw0mxgIaYX6R8ODrdkLLPwBqHTtYHJSmzzd+QeytSugzQ0Vg4c5rDky5VgkoowbZQahCbsv1rT1KW72MPIkevw==", "os": ["darwin"], "cpu": ["arm64"]}, "@esbuild/darwin-x64@0.18.20": {"integrity": "sha512-pc5gxlMDxzm513qPGbCbDukOdsGtKhfxD1zJKXjCCcU7ju50O7MeAZ8c4krSJcOIJGFR+qx21yMMVYwiQvyTyQ==", "os": ["darwin"], "cpu": ["x64"]}, "@esbuild/darwin-x64@0.25.8": {"integrity": "sha512-Vh2gLxxHnuoQ+GjPNvDSDRpoBCUzY4Pu0kBqMBDlK4fuWbKgGtmDIeEC081xi26PPjn+1tct+Bh8FjyLlw1Zlg==", "os": ["darwin"], "cpu": ["x64"]}, "@esbuild/freebsd-arm64@0.18.20": {"integrity": "sha512-yqDQHy4QHevpMAaxhhIwYPMv1NECwOvIpGCZkECn8w2WFHXjEwrBn3CeNIYsibZ/iZEUemj++M26W3cNR5h+Tw==", "os": ["freebsd"], "cpu": ["arm64"]}, "@esbuild/freebsd-arm64@0.25.8": {"integrity": "sha512-YPJ7hDQ9DnNe5vxOm6jaie9QsTwcKedPvizTVlqWG9GBSq+BuyWEDazlGaDTC5NGU4QJd666V0yqCBL2oWKPfA==", "os": ["freebsd"], "cpu": ["arm64"]}, "@esbuild/freebsd-x64@0.18.20": {"integrity": "sha512-tgWRPPuQsd3RmBZwarGVHZQvtzfEBOreNuxEMKFcd5DaDn2PbBxfwLcj4+aenoh7ctXcbXmOQIn8HI6mCSw5MQ==", "os": ["freebsd"], "cpu": ["x64"]}, "@esbuild/freebsd-x64@0.25.8": {"integrity": "sha512-MmaEXxQRdXNFsRN/KcIimLnSJrk2r5H8v+WVafRWz5xdSVmWLoITZQXcgehI2ZE6gioE6HirAEToM/RvFBeuhw==", "os": ["freebsd"], "cpu": ["x64"]}, "@esbuild/linux-arm64@0.18.20": {"integrity": "sha512-2YbscF+UL7SQAVIpnWvYwM+3LskyDmPhe31pE7/aoTMFKKzIc9lLbyGUpmmb8a8AixOL61sQ/mFh3jEjHYFvdA==", "os": ["linux"], "cpu": ["arm64"]}, "@esbuild/linux-arm64@0.25.8": {"integrity": "sha512-WIgg00ARWv/uYLU7lsuDK00d/hHSfES5BzdWAdAig1ioV5kaFNrtK8EqGcUBJhYqotlUByUKz5Qo6u8tt7iD/w==", "os": ["linux"], "cpu": ["arm64"]}, "@esbuild/linux-arm@0.18.20": {"integrity": "sha512-/5bHkMWnq1EgKr1V+Ybz3s1hWXok7mDFUMQ4cG10AfW3wL02PSZi5kFpYKrptDsgb2WAJIvRcDm+qIvXf/apvg==", "os": ["linux"], "cpu": ["arm"]}, "@esbuild/linux-arm@0.25.8": {"integrity": "sha512-FuzEP9BixzZohl1kLf76KEVOsxtIBFwCaLupVuk4eFVnOZfU+Wsn+x5Ryam7nILV2pkq2TqQM9EZPsOBuMC+kg==", "os": ["linux"], "cpu": ["arm"]}, "@esbuild/linux-ia32@0.18.20": {"integrity": "sha512-P4etWwq6IsReT0E1KHU40bOnzMHoH73aXp96Fs8TIT6z9Hu8G6+0SHSw9i2isWrD2nbx2qo5yUqACgdfVGx7TA==", "os": ["linux"], "cpu": ["ia32"]}, "@esbuild/linux-ia32@0.25.8": {"integrity": "sha512-A1D9YzRX1i+1AJZuFFUMP1E9fMaYY+GnSQil9Tlw05utlE86EKTUA7RjwHDkEitmLYiFsRd9HwKBPEftNdBfjg==", "os": ["linux"], "cpu": ["ia32"]}, "@esbuild/linux-loong64@0.18.20": {"integrity": "sha512-nXW8nqBTrOpDLPgPY9uV+/1DjxoQ7DoB2N8eocyq8I9XuqJ7BiAMDMf9n1xZM9TgW0J8zrquIb/A7s3BJv7rjg==", "os": ["linux"], "cpu": ["loong64"]}, "@esbuild/linux-loong64@0.25.8": {"integrity": "sha512-O7k1J/dwHkY1RMVvglFHl1HzutGEFFZ3kNiDMSOyUrB7WcoHGf96Sh+64nTRT26l3GMbCW01Ekh/ThKM5iI7hQ==", "os": ["linux"], "cpu": ["loong64"]}, "@esbuild/linux-mips64el@0.18.20": {"integrity": "sha512-d5NeaXZcHp8PzYy5VnXV3VSd2D328Zb+9dEq5HE6bw6+N86JVPExrA6O68OPwobntbNJ0pzCpUFZTo3w0GyetQ==", "os": ["linux"], "cpu": ["mips64el"]}, "@esbuild/linux-mips64el@0.25.8": {"integrity": "sha512-uv+dqfRazte3BzfMp8PAQXmdGHQt2oC/y2ovwpTteqrMx2lwaksiFZ/bdkXJC19ttTvNXBuWH53zy/aTj1FgGw==", "os": ["linux"], "cpu": ["mips64el"]}, "@esbuild/linux-ppc64@0.18.20": {"integrity": "sha512-WHPyeScRNcmANnLQkq6AfyXRFr5D6N2sKgkFo2FqguP44Nw2eyDlbTdZwd9GYk98DZG9QItIiTlFLHJHjxP3FA==", "os": ["linux"], "cpu": ["ppc64"]}, "@esbuild/linux-ppc64@0.25.8": {"integrity": "sha512-GyG0KcMi1GBavP5JgAkkstMGyMholMDybAf8wF5A70CALlDM2p/f7YFE7H92eDeH/VBtFJA5MT4nRPDGg4JuzQ==", "os": ["linux"], "cpu": ["ppc64"]}, "@esbuild/linux-riscv64@0.18.20": {"integrity": "sha512-WSxo6h5ecI5XH34KC7w5veNnKkju3zBRLEQNY7mv5mtBmrP/MjNBCAlsM2u5hDBlS3NGcTQpoBvRzqBcRtpq1A==", "os": ["linux"], "cpu": ["riscv64"]}, "@esbuild/linux-riscv64@0.25.8": {"integrity": "sha512-rAqDYFv3yzMrq7GIcen3XP7TUEG/4LK86LUPMIz6RT8A6pRIDn0sDcvjudVZBiiTcZCY9y2SgYX2lgK3AF+1eg==", "os": ["linux"], "cpu": ["riscv64"]}, "@esbuild/linux-s390x@0.18.20": {"integrity": "sha512-+8231GMs3mAEth6Ja1iK0a1sQ3ohfcpzpRLH8uuc5/KVDFneH6jtAJLFGafpzpMRO6DzJ6AvXKze9LfFMrIHVQ==", "os": ["linux"], "cpu": ["s390x"]}, "@esbuild/linux-s390x@0.25.8": {"integrity": "sha512-Xutvh6VjlbcHpsIIbwY8GVRbwoviWT19tFhgdA7DlenLGC/mbc3lBoVb7jxj9Z+eyGqvcnSyIltYUrkKzWqSvg==", "os": ["linux"], "cpu": ["s390x"]}, "@esbuild/linux-x64@0.18.20": {"integrity": "sha512-UYqiqemphJcNsFEskc73jQ7B9jgwjWrSayxawS6UVFZGWrAAtkzjxSqnoclCXxWtfwLdzU+vTpcNYhpn43uP1w==", "os": ["linux"], "cpu": ["x64"]}, "@esbuild/linux-x64@0.25.8": {"integrity": "sha512-ASFQhgY4ElXh3nDcOMTkQero4b1lgubskNlhIfJrsH5OKZXDpUAKBlNS0Kx81jwOBp+HCeZqmoJuihTv57/jvQ==", "os": ["linux"], "cpu": ["x64"]}, "@esbuild/netbsd-arm64@0.25.8": {"integrity": "sha512-d1KfruIeohqAi6SA+gENMuObDbEjn22olAR7egqnkCD9DGBG0wsEARotkLgXDu6c4ncgWTZJtN5vcgxzWRMzcw==", "os": ["netbsd"], "cpu": ["arm64"]}, "@esbuild/netbsd-x64@0.18.20": {"integrity": "sha512-iO1c++VP6xUBUmltHZoMtCUdPlnPGdBom6IrO4gyKPFFVBKioIImVooR5I83nTew5UOYrk3gIJhbZh8X44y06A==", "os": ["netbsd"], "cpu": ["x64"]}, "@esbuild/netbsd-x64@0.25.8": {"integrity": "sha512-nVDCkrvx2ua+XQNyfrujIG38+YGyuy2Ru9kKVNyh5jAys6n+l44tTtToqHjino2My8VAY6Lw9H7RI73XFi66Cg==", "os": ["netbsd"], "cpu": ["x64"]}, "@esbuild/openbsd-arm64@0.25.8": {"integrity": "sha512-j8HgrDuSJFAujkivSMSfPQSAa5Fxbvk4rgNAS5i3K+r8s1X0p1uOO2Hl2xNsGFppOeHOLAVgYwDVlmxhq5h+SQ==", "os": ["openbsd"], "cpu": ["arm64"]}, "@esbuild/openbsd-x64@0.18.20": {"integrity": "sha512-e5e4YSsuQfX4cxcygw/UCPIEP6wbIL+se3sxPdCiMbFLBWu0eiZOJ7WoD+ptCLrmjZBK1Wk7I6D/I3NglUGOxg==", "os": ["openbsd"], "cpu": ["x64"]}, "@esbuild/openbsd-x64@0.25.8": {"integrity": "sha512-1h8MUAwa0VhNCDp6Af0HToI2TJFAn1uqT9Al6DJVzdIBAd21m/G0Yfc77KDM3uF3T/YaOgQq3qTJHPbTOInaIQ==", "os": ["openbsd"], "cpu": ["x64"]}, "@esbuild/openharmony-arm64@0.25.8": {"integrity": "sha512-r2nVa5SIK9tSWd0kJd9HCffnDHKchTGikb//9c7HX+r+wHYCpQrSgxhlY6KWV1nFo1l4KFbsMlHk+L6fekLsUg==", "os": ["openharmony"], "cpu": ["arm64"]}, "@esbuild/sunos-x64@0.18.20": {"integrity": "sha512-kDbFRFp0YpTQVVrqUd5FTYmWo45zGaXe0X8E1G/LKFC0v8x0vWrhOWSLITcCn63lmZIxfOMXtCfti/RxN/0wnQ==", "os": ["sunos"], "cpu": ["x64"]}, "@esbuild/sunos-x64@0.25.8": {"integrity": "sha512-zUlaP2S12YhQ2UzUfcCuMDHQFJyKABkAjvO5YSndMiIkMimPmxA+BYSBikWgsRpvyxuRnow4nS5NPnf9fpv41w==", "os": ["sunos"], "cpu": ["x64"]}, "@esbuild/win32-arm64@0.18.20": {"integrity": "sha512-ddYFR6ItYgoaq4v4JmQQaAI5s7npztfV4Ag6NrhiaW0RrnOXqBkgwZLofVTlq1daVTQNhtI5oieTvkRPfZrePg==", "os": ["win32"], "cpu": ["arm64"]}, "@esbuild/win32-arm64@0.25.8": {"integrity": "sha512-YEGFFWESlPva8hGL+zvj2z/SaK+pH0SwOM0Nc/d+rVnW7GSTFlLBGzZkuSU9kFIGIo8q9X3ucpZhu8PDN5A2sQ==", "os": ["win32"], "cpu": ["arm64"]}, "@esbuild/win32-ia32@0.18.20": {"integrity": "sha512-Wv7QBi3ID/rROT08SABTS7eV4hX26sVduqDOTe1MvGMjNd3EjOz4b7zeexIR62GTIEKrfJXKL9LFxTYgkyeu7g==", "os": ["win32"], "cpu": ["ia32"]}, "@esbuild/win32-ia32@0.25.8": {"integrity": "sha512-hiGgGC6KZ5LZz58OL/+qVVoZiuZlUYlYHNAmczOm7bs2oE1XriPFi5ZHHrS8ACpV5EjySrnoCKmcbQMN+ojnHg==", "os": ["win32"], "cpu": ["ia32"]}, "@esbuild/win32-x64@0.18.20": {"integrity": "sha512-kTdfRcSiDfQca/y9QIkng02avJ+NCaQvrMejlsB3RRv5sE9rRoeBPISaZpKxHELzRxZyLvNts1P27W3wV+8geQ==", "os": ["win32"], "cpu": ["x64"]}, "@esbuild/win32-x64@0.25.8": {"integrity": "sha512-cn3Yr7+OaaZq1c+2pe+8yxC8E144SReCQjN6/2ynubzYjvyqZjTXfQJpAcQpsdJq3My7XADANiYGHoFC69pLQw==", "os": ["win32"], "cpu": ["x64"]}, "@jridgewell/gen-mapping@0.3.12": {"integrity": "sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==", "dependencies": ["@jridgewell/sourcemap-codec", "@jridgewell/trace-mapping"]}, "@jridgewell/resolve-uri@3.1.2": {"integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw=="}, "@jridgewell/sourcemap-codec@1.5.4": {"integrity": "sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw=="}, "@jridgewell/trace-mapping@0.3.29": {"integrity": "sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ==", "dependencies": ["@jridgewell/resolve-uri", "@jridgewell/sourcemap-codec"]}, "@parcel/watcher-android-arm64@2.5.1": {"integrity": "sha512-KF8+j9nNbUN8vzOFDpRMsaKBHZ/mcjEjMToVMJOhTozkDonQFFrRcfdLWn6yWKCmJKmdVxSgHiYvTCef4/qcBA==", "os": ["android"], "cpu": ["arm64"]}, "@parcel/watcher-darwin-arm64@2.5.1": {"integrity": "sha512-eAzPv5osDmZyBhou8PoF4i6RQXAfeKL9tjb3QzYuccXFMQU0ruIc/POh30ePnaOyD1UXdlKguHBmsTs53tVoPw==", "os": ["darwin"], "cpu": ["arm64"]}, "@parcel/watcher-darwin-x64@2.5.1": {"integrity": "sha512-1ZXDthrnNmwv10A0/3AJNZ9JGlzrF82i3gNQcWOzd7nJ8aj+ILyW1MTxVk35Db0u91oD5Nlk9MBiujMlwmeXZg==", "os": ["darwin"], "cpu": ["x64"]}, "@parcel/watcher-freebsd-x64@2.5.1": {"integrity": "sha512-SI4eljM7Flp9yPuKi8W0ird8TI/JK6CSxju3NojVI6BjHsTyK7zxA9urjVjEKJ5MBYC+bLmMcbAWlZ+rFkLpJQ==", "os": ["freebsd"], "cpu": ["x64"]}, "@parcel/watcher-linux-arm-glibc@2.5.1": {"integrity": "sha512-RCdZlEyTs8geyBkkcnPWvtXLY44BCeZKmGYRtSgtwwnHR4dxfHRG3gR99XdMEdQ7KeiDdasJwwvNSF5jKtDwdA==", "os": ["linux"], "cpu": ["arm"]}, "@parcel/watcher-linux-arm-musl@2.5.1": {"integrity": "sha512-6E+m/Mm1t1yhB8X412stiKFG3XykmgdIOqhjWj+VL8oHkKABfu/gjFj8DvLrYVHSBNC+/u5PeNrujiSQ1zwd1Q==", "os": ["linux"], "cpu": ["arm"]}, "@parcel/watcher-linux-arm64-glibc@2.5.1": {"integrity": "sha512-LrGp+f02yU3BN9A+DGuY3v3bmnFUggAITBGriZHUREfNEzZh/GO06FF5u2kx8x+GBEUYfyTGamol4j3m9ANe8w==", "os": ["linux"], "cpu": ["arm64"]}, "@parcel/watcher-linux-arm64-musl@2.5.1": {"integrity": "sha512-cFOjABi92pMYRXS7AcQv9/M1YuKRw8SZniCDw0ssQb/noPkRzA+HBDkwmyOJYp5wXcsTrhxO0zq1U11cK9jsFg==", "os": ["linux"], "cpu": ["arm64"]}, "@parcel/watcher-linux-x64-glibc@2.5.1": {"integrity": "sha512-GcESn8NZySmfwlTsIur+49yDqSny2IhPeZfXunQi48DMugKeZ7uy1FX83pO0X22sHntJ4Ub+9k34XQCX+oHt2A==", "os": ["linux"], "cpu": ["x64"]}, "@parcel/watcher-linux-x64-musl@2.5.1": {"integrity": "sha512-n0E2EQbatQ3bXhcH2D1XIAANAcTZkQICBPVaxMeaCVBtOpBZpWJuf7LwyWPSBDITb7In8mqQgJ7gH8CILCURXg==", "os": ["linux"], "cpu": ["x64"]}, "@parcel/watcher-win32-arm64@2.5.1": {"integrity": "sha512-RFzklRvmc3PkjKjry3hLF9wD7ppR4AKcWNzH7kXR7GUe0Igb3Nz8fyPwtZCSquGrhU5HhUNDr/mKBqj7tqA2Vw==", "os": ["win32"], "cpu": ["arm64"]}, "@parcel/watcher-win32-ia32@2.5.1": {"integrity": "sha512-c2KkcVN+NJmuA7CGlaGD1qJh1cLfDnQsHjE89E60vUEMlqduHGCdCLJCID5geFVM0dOtA3ZiIO8BoEQmzQVfpQ==", "os": ["win32"], "cpu": ["ia32"]}, "@parcel/watcher-win32-x64@2.5.1": {"integrity": "sha512-9lHBdJITeNR++EvSQVUcaZoWupyHfXe1jZvGZ06O/5MflPcuPLtEphScIBL+AiCWBO46tDSHzWyD0uDmmZqsgA==", "os": ["win32"], "cpu": ["x64"]}, "@parcel/watcher@2.5.1": {"integrity": "sha512-dfUnCxiN9H4ap84DvD2ubjw+3vUNpstxa0TneY/Paat8a3R4uQZDLSvWjmznAY/DoahqTHl9V46HF/Zs3F29pg==", "dependencies": ["detect-libc", "is-glob", "micromatch", "node-addon-api"], "optionalDependencies": ["@parcel/watcher-android-arm64", "@parcel/watcher-darwin-arm64", "@parcel/watcher-darwin-x64", "@parcel/watcher-freebsd-x64", "@parcel/watcher-linux-arm-glibc", "@parcel/watcher-linux-arm-musl", "@parcel/watcher-linux-arm64-glibc", "@parcel/watcher-linux-arm64-musl", "@parcel/watcher-linux-x64-glibc", "@parcel/watcher-linux-x64-musl", "@parcel/watcher-win32-arm64", "@parcel/watcher-win32-ia32", "@parcel/watcher-win32-x64"], "scripts": true}, "@playwright/test@1.54.2": {"integrity": "sha512-A+znathYxPf+72riFd1r1ovOLqsIIB0jKIoPjyK2kqEIe30/6jF6BC7QNluHuwUmsD2tv1XZVugN8GqfTMOxsA==", "dependencies": ["playwright"], "bin": true}, "@polka/url@1.0.0-next.29": {"integrity": "sha512-wwQAWhWSuHaag8c4q/KN/vCoeOJYshAIvMQwD4GpSb3OiZklFfvAgmj0VCBBImRpuF/aFgIRzllXlVX93Jevww=="}, "@rollup/rollup-android-arm-eabi@4.46.2": {"integrity": "sha512-Zj3Hl6sN34xJtMv7Anwb5Gu01yujyE/cLBDB2gnHTAHaWS1Z38L7kuSG+oAh0giZMqG060f/YBStXtMH6FvPMA==", "os": ["android"], "cpu": ["arm"]}, "@rollup/rollup-android-arm64@4.46.2": {"integrity": "sha512-nTeCWY83kN64oQ5MGz3CgtPx8NSOhC5lWtsjTs+8JAJNLcP3QbLCtDDgUKQc/Ro/frpMq4SHUaHN6AMltcEoLQ==", "os": ["android"], "cpu": ["arm64"]}, "@rollup/rollup-darwin-arm64@4.46.2": {"integrity": "sha512-HV7bW2Fb/F5KPdM/9bApunQh68YVDU8sO8BvcW9OngQVN3HHHkw99wFupuUJfGR9pYLLAjcAOA6iO+evsbBaPQ==", "os": ["darwin"], "cpu": ["arm64"]}, "@rollup/rollup-darwin-x64@4.46.2": {"integrity": "sha512-SSj8TlYV5nJixSsm/y3QXfhspSiLYP11zpfwp6G/YDXctf3Xkdnk4woJIF5VQe0of2OjzTt8EsxnJDCdHd2xMA==", "os": ["darwin"], "cpu": ["x64"]}, "@rollup/rollup-freebsd-arm64@4.46.2": {"integrity": "sha512-ZyrsG4TIT9xnOlLsSSi9w/X29tCbK1yegE49RYm3tu3wF1L/B6LVMqnEWyDB26d9Ecx9zrmXCiPmIabVuLmNSg==", "os": ["freebsd"], "cpu": ["arm64"]}, "@rollup/rollup-freebsd-x64@4.46.2": {"integrity": "sha512-pCgHFoOECwVCJ5GFq8+gR8SBKnMO+xe5UEqbemxBpCKYQddRQMgomv1104RnLSg7nNvgKy05sLsY51+OVRyiVw==", "os": ["freebsd"], "cpu": ["x64"]}, "@rollup/rollup-linux-arm-gnueabihf@4.46.2": {"integrity": "sha512-EtP8aquZ0xQg0ETFcxUbU71MZlHaw9MChwrQzatiE8U/bvi5uv/oChExXC4mWhjiqK7azGJBqU0tt5H123SzVA==", "os": ["linux"], "cpu": ["arm"]}, "@rollup/rollup-linux-arm-musleabihf@4.46.2": {"integrity": "sha512-qO7F7U3u1nfxYRPM8HqFtLd+raev2K137dsV08q/LRKRLEc7RsiDWihUnrINdsWQxPR9jqZ8DIIZ1zJJAm5PjQ==", "os": ["linux"], "cpu": ["arm"]}, "@rollup/rollup-linux-arm64-gnu@4.46.2": {"integrity": "sha512-3dRaqLfcOXYsfvw5xMrxAk9Lb1f395gkoBYzSFcc/scgRFptRXL9DOaDpMiehf9CO8ZDRJW2z45b6fpU5nwjng==", "os": ["linux"], "cpu": ["arm64"]}, "@rollup/rollup-linux-arm64-musl@4.46.2": {"integrity": "sha512-fhHFTutA7SM+IrR6lIfiHskxmpmPTJUXpWIsBXpeEwNgZzZZSg/q4i6FU4J8qOGyJ0TR+wXBwx/L7Ho9z0+uDg==", "os": ["linux"], "cpu": ["arm64"]}, "@rollup/rollup-linux-loongarch64-gnu@4.46.2": {"integrity": "sha512-i7wfGFXu8x4+FRqPymzjD+Hyav8l95UIZ773j7J7zRYc3Xsxy2wIn4x+llpunexXe6laaO72iEjeeGyUFmjKeA==", "os": ["linux"], "cpu": ["loong64"]}, "@rollup/rollup-linux-ppc64-gnu@4.46.2": {"integrity": "sha512-B/l0dFcHVUnqcGZWKcWBSV2PF01YUt0Rvlurci5P+neqY/yMKchGU8ullZvIv5e8Y1C6wOn+U03mrDylP5q9Yw==", "os": ["linux"], "cpu": ["ppc64"]}, "@rollup/rollup-linux-riscv64-gnu@4.46.2": {"integrity": "sha512-32k4ENb5ygtkMwPMucAb8MtV8olkPT03oiTxJbgkJa7lJ7dZMr0GCFJlyvy+K8iq7F/iuOr41ZdUHaOiqyR3iQ==", "os": ["linux"], "cpu": ["riscv64"]}, "@rollup/rollup-linux-riscv64-musl@4.46.2": {"integrity": "sha512-t5B2loThlFEauloaQkZg9gxV05BYeITLvLkWOkRXogP4qHXLkWSbSHKM9S6H1schf/0YGP/qNKtiISlxvfmmZw==", "os": ["linux"], "cpu": ["riscv64"]}, "@rollup/rollup-linux-s390x-gnu@4.46.2": {"integrity": "sha512-YKjekwTEKgbB7n17gmODSmJVUIvj8CX7q5442/CK80L8nqOUbMtf8b01QkG3jOqyr1rotrAnW6B/qiHwfcuWQA==", "os": ["linux"], "cpu": ["s390x"]}, "@rollup/rollup-linux-x64-gnu@4.46.2": {"integrity": "sha512-Jj5a9RUoe5ra+MEyERkDKLwTXVu6s3aACP51nkfnK9wJTraCC8IMe3snOfALkrjTYd2G1ViE1hICj0fZ7ALBPA==", "os": ["linux"], "cpu": ["x64"]}, "@rollup/rollup-linux-x64-musl@4.46.2": {"integrity": "sha512-7kX69DIrBeD7yNp4A5b81izs8BqoZkCIaxQaOpumcJ1S/kmqNFjPhDu1LHeVXv0SexfHQv5cqHsxLOjETuqDuA==", "os": ["linux"], "cpu": ["x64"]}, "@rollup/rollup-win32-arm64-msvc@4.46.2": {"integrity": "sha512-wiJWMIpeaak/jsbaq2HMh/rzZxHVW1rU6coyeNNpMwk5isiPjSTx0a4YLSlYDwBH/WBvLz+EtsNqQScZTLJy3g==", "os": ["win32"], "cpu": ["arm64"]}, "@rollup/rollup-win32-ia32-msvc@4.46.2": {"integrity": "sha512-gBgaUDESVzMgWZhcyjfs9QFK16D8K6QZpwAaVNJxYDLHWayOta4ZMjGm/vsAEy3hvlS2GosVFlBlP9/Wb85DqQ==", "os": ["win32"], "cpu": ["ia32"]}, "@rollup/rollup-win32-x64-msvc@4.46.2": {"integrity": "sha512-CvUo2ixeIQGtF6WvuB87XWqPQkoFAFqW+HUo/WzHwuHDvIwZCtjdWXoYCcr06iKGydiqTclC4jU/TNObC/xKZg==", "os": ["win32"], "cpu": ["x64"]}, "@standard-schema/spec@1.0.0": {"integrity": "sha512-m2bOd0f2RT9k8QJx1JN85cZYyH1RqFBdlwtkSlf4tBDYLCiiZnv1fIIwacK6cqwXavOydf0NPToMQgpKq+dVlA=="}, "@supabase/auth-js@2.71.1": {"integrity": "sha512-mMIQHBRc+SKpZFRB2qtupuzulaUhFYupNyxqDj5Jp/LyPvcWvjaJzZzObv6URtL/O6lPxkanASnotGtNpS3H2Q==", "dependencies": ["@supabase/node-fetch"]}, "@supabase/functions-js@2.4.5": {"integrity": "sha512-v5GSqb9zbosquTo6gBwIiq7W9eQ7rE5QazsK/ezNiQXdCbY+bH8D9qEaBIkhVvX4ZRW5rP03gEfw5yw9tiq4EQ==", "dependencies": ["@supabase/node-fetch"]}, "@supabase/node-fetch@2.6.15": {"integrity": "sha512-1ibVeYUacxWYi9i0cf5efil6adJ9WRyZBLivgjs+AUpewx1F3xPi7gLgaASI2SmIQxPoCEjAsLAzKPgMJVgOUQ==", "dependencies": ["whatwg-url@5.0.0"]}, "@supabase/postgrest-js@1.19.4": {"integrity": "sha512-O4soKqKtZIW3olqmbXXbKugUtByD2jPa8kL2m2c1oozAO11uCcGrRhkZL0kVxjBLrXHE0mdSkFsMj7jDSfyNpw==", "dependencies": ["@supabase/node-fetch"]}, "@supabase/realtime-js@2.15.0": {"integrity": "sha512-SEIWApsxyoAe68WU2/5PCCuBwa11LL4Bb8K3r2FHCt3ROpaTthmDiWEhnLMGayP05N4QeYrMk0kyTZOwid/Hjw==", "dependencies": ["@supabase/node-fetch", "@types/phoenix", "@types/ws", "ws"]}, "@supabase/storage-js@2.10.4": {"integrity": "sha512-cvL02GarJVFcNoWe36VBybQqTVRq6wQSOCvTS64C+eyuxOruFIm1utZAY0xi2qKtHJO3EjKaj8iWJKySusDmAQ==", "dependencies": ["@supabase/node-fetch"]}, "@supabase/supabase-js@2.54.0": {"integrity": "sha512-DLw83YwBfAaFiL3oWV26+sHRdeCGtxmIKccjh/Pndze3BWM4fZghzYKhk3ElOQU8Bluq4AkkCJ5bM5Szl/sfRg==", "dependencies": ["@supabase/auth-js", "@supabase/functions-js", "@supabase/node-fetch", "@supabase/postgrest-js", "@supabase/realtime-js", "@supabase/storage-js"]}, "@sveltejs/acorn-typescript@1.0.5_acorn@8.15.0": {"integrity": "sha512-IwQk4yfwLdibDlrXVE04jTZYlLnwsTT2PIOQQGNLWfjavGifnk1JD1LcZjZaBTRcxZu2FfPfNLOE04DSu9lqtQ==", "dependencies": ["acorn"]}, "@sveltejs/adapter-auto@6.0.2_@sveltejs+kit@2.27.3__@sveltejs+vite-plugin-svelte@6.1.1___svelte@5.38.0____acorn@8.15.0___vite@7.1.1____sass@1.90.0____picomatch@4.0.3___sass@1.90.0__svelte@5.38.0___acorn@8.15.0__vite@7.1.1___sass@1.90.0___picomatch@4.0.3__acorn@8.15.0__sass@1.90.0_@sveltejs+vite-plugin-svelte@6.1.1__svelte@5.38.0___acorn@8.15.0__vite@7.1.1___sass@1.90.0___picomatch@4.0.3__sass@1.90.0_svelte@5.38.0__acorn@8.15.0_vite@7.1.1__sass@1.90.0__picomatch@4.0.3_sass@1.90.0": {"integrity": "sha512-SEyhlW5dMcsAZBONGCKUlK3+Ywr+8I2OupJh7OJR0URqsGzvkcWKRkBXqWifWVJnehjCGVLaqD/94aZ3xpj1jQ==", "dependencies": ["@sveltejs/kit"]}, "@sveltejs/kit@2.27.3_@sveltejs+vite-plugin-svelte@6.1.1__svelte@5.38.0___acorn@8.15.0__vite@7.1.1___sass@1.90.0___picomatch@4.0.3__sass@1.90.0_svelte@5.38.0__acorn@8.15.0_vite@7.1.1__sass@1.90.0__picomatch@4.0.3_acorn@8.15.0_sass@1.90.0": {"integrity": "sha512-jiG3NGZ8RRpi+ncjVnX+oR7uWEgzy//3YLGcTU5mHtjGraeGyNDr7GJFHlk7z0vi8bMXpXIUkEXj6p70FJmHvw==", "dependencies": ["@standard-schema/spec", "@sveltejs/acorn-typescript", "@sveltejs/vite-plugin-svelte", "@types/cookie", "acorn", "cookie", "devalue", "esm-env", "kleur", "magic-string", "mrmime", "sade", "set-cookie-parser", "sirv", "svelte", "vite"], "bin": true}, "@sveltejs/vite-plugin-svelte-inspector@5.0.0_@sveltejs+vite-plugin-svelte@6.1.1__svelte@5.38.0___acorn@8.15.0__vite@7.1.1___sass@1.90.0___picomatch@4.0.3__sass@1.90.0_svelte@5.38.0__acorn@8.15.0_vite@7.1.1__sass@1.90.0__picomatch@4.0.3_sass@1.90.0": {"integrity": "sha512-iwQ8Z4ET6ZFSt/gC+tVfcsSBHwsqc6RumSaiLUkAurW3BCpJam65cmHw0oOlDMTO0u+PZi9hilBRYN+LZNHTUQ==", "dependencies": ["@sveltejs/vite-plugin-svelte", "debug", "svelte", "vite"]}, "@sveltejs/vite-plugin-svelte@6.1.1_svelte@5.38.0__acorn@8.15.0_vite@7.1.1__sass@1.90.0__picomatch@4.0.3_sass@1.90.0": {"integrity": "sha512-vB0Vq47Js7C11L2JrwhncIAoDNkdKDPI500SjLSb34X48dDcsSH5JpLl0cHT0sfO997BrzAS6PKjiZEey/S0VQ==", "dependencies": ["@sveltejs/vite-plugin-svelte-inspector", "debug", "deepmerge", "kleur", "magic-string", "svelte", "vite", "vitefu"]}, "@testing-library/dom@10.4.1": {"integrity": "sha512-o4PXJQidqJl82ckFaXUeoAW+XysPLauYI43Abki5hABd853iMhitooc6znOnczgbTYmEP6U6/y1ZyKAIsvMKGg==", "dependencies": ["@babel/code-frame", "@babel/runtime", "@types/aria-query", "aria-query@5.3.0", "dom-accessibility-api@0.5.16", "lz-string", "picocolors", "pretty-format"]}, "@testing-library/jest-dom@6.6.4": {"integrity": "sha512-xDXgLjVunjHqczScfkCJ9iyjdNOVHvvCdqHSSxwM9L0l/wHkTRum67SDc020uAlCoqktJplgO2AAQeLP1wgqDQ==", "dependencies": ["@adobe/css-tools", "aria-query@5.3.2", "css.escape", "dom-accessibility-api@0.6.3", "lodash", "picocolors", "redent"]}, "@testing-library/svelte@5.2.8_svelte@5.38.0__acorn@8.15.0_vite@7.1.1__sass@1.90.0__picomatch@4.0.3_vitest@3.2.4__jsdom@26.1.0__vite@7.1.1___sass@1.90.0___picomatch@4.0.3__sass@1.90.0_sass@1.90.0_jsdom@26.1.0": {"integrity": "sha512-ucQOtGsJhtawOEtUmbR4rRh53e6RbM1KUluJIXRmh6D4UzxR847iIqqjRtg9mHNFmGQ8Vkam9yVcR5d1mhIHKA==", "dependencies": ["@testing-library/dom", "svelte", "vite", "vitest"], "optionalPeers": ["vite", "vitest"]}, "@threejs-kit/instanced-sprite-mesh@2.5.1_three@0.179.1_@types+three@0.179.0": {"integrity": "sha512-pmt1ALRhbHhCJQTj2FuthH6PeLIeaM4hOuS2JO3kWSwlnvx/9xuUkjFR3JOi/myMqsH7pSsLIROSaBxDfttjeA==", "dependencies": ["diet-sprite", "earcut", "maath", "three", "three-instanced-uniforms-mesh", "troika-three-utils"]}, "@threlte/core@8.1.4_svelte@5.38.0__acorn@8.15.0_three@0.179.1": {"integrity": "sha512-zeZS49roWpWhqHkMAcTqg60IbjX2d7NHPg4ww+sKChMBRL2J4MNTQp7Gm4U14RpQeEXFR9itOaPfqMwr93dPMQ==", "dependencies": ["mitt", "svelte", "three"]}, "@threlte/extras@9.4.4_svelte@5.38.0__acorn@8.15.0_three@0.179.1_@types+three@0.179.0": {"integrity": "sha512-GeTHFAaAaR3a4JO0KJaMiYueUlGixYyQgDf+09z14kvSMrlGXL/fZCD0172u6o6uoQNLFei5TjrIxytcJLbrQw==", "dependencies": ["@threejs-kit/instanced-sprite-mesh", "camera-controls", "svelte", "three", "three-mesh-bvh", "three-perf", "three-viewport-gizmo", "troika-three-text"]}, "@tweenjs/tween.js@23.1.3": {"integrity": "sha512-vJmvvwFxYuGnF2axRtPYocag6Clbb5YS7kLL+SO/TeVFzHqDIWrNKYtcsPMibjDx9O+bu+psAy9NKfWklassUA=="}, "@types/aria-query@5.0.4": {"integrity": "sha512-rfT93uj5s0PRL7EzccGMs3brplhcrghnDoV26NqKhCAS1hVo+WdNsPvE/yb6ilfr5hi2MEk6d5EWJTKdxg8jVw=="}, "@types/chai@5.2.2": {"integrity": "sha512-8kB30R7Hwqf40JPiKhVzodJs2Qc1ZJ5zuT3uzw5Hq/dhNCl3G3l83jfpdI1e20BP348+fV7VIL/+FxaXkqBmWg==", "dependencies": ["@types/deep-eql"]}, "@types/cookie@0.6.0": {"integrity": "sha512-4Kh9a6B2bQciAhf7FSuMRRkUWecJgJu9nPnx3yzpsfXX/c50REIqpHY4C82bXP90qrLtXtkDxTZosYO3UpOwlA=="}, "@types/deep-eql@4.0.2": {"integrity": "sha512-c9h9dVVMigMPc4bwTvC5dxqtqJZwQPePsWjPlpSOnojbor6pGqdk541lfA7AqFQr5pB1BRdq0juY9db81BwyFw=="}, "@types/estree@1.0.8": {"integrity": "sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w=="}, "@types/node@22.15.15": {"integrity": "sha512-R5muMcZob3/Jjchn5LcO8jdKwSCbzqmPB6ruBxMcf9kbxtniZHP327s6C37iOfuw8mbKK3cAQa7sEl7afLrQ8A==", "dependencies": ["undici-types"]}, "@types/phoenix@1.6.6": {"integrity": "sha512-PIzZZlEppgrpoT2QgbnDU+MMzuR6BbCjllj0bM70lWoejMeNJAxCchxnv7J3XFkI8MpygtRpzXrIlmWUBclP5A=="}, "@types/stats.js@0.17.4": {"integrity": "sha512-jIBvWWShCvlBqBNIZt0KAshWpvSjhkwkEu4ZUcASoAvhmrgAUI2t1dXrjSL4xXVLB4FznPrIsX3nKXFl/Dt4vA=="}, "@types/three@0.179.0": {"integrity": "sha512-VgbFG2Pgsm84BqdegZzr7w2aKbQxmgzIu4Dy7/75ygiD/0P68LKmp5ie08KMPNqGTQwIge8s6D1guZf1RnZE0A==", "dependencies": ["@dimforge/rapier3d-compat", "@tweenjs/tween.js", "@types/stats.js", "@types/webxr", "@webgpu/types", "fflate", "meshoptimizer"]}, "@types/webxr@0.5.22": {"integrity": "sha512-Vr6Stjv5jPRqH690f5I5GLjVk8GSsoQSYJ2FVd/3jJF7KaqfwPi3ehfBS96mlQ2kPCwZaX6U0rG2+NGHBKkA/A=="}, "@types/ws@8.18.1": {"integrity": "sha512-ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg==", "dependencies": ["@types/node"]}, "@vaie/hui@0.0.0_svelte@5.38.0__acorn@8.15.0_sass@1.90.0_typescript@5.9.2": {"integrity": "sha512-dIF/lBAlo5QL2Bm18Gby/QVp21GO4I7dSmya5JE+4Upc38cVyEXE/VME6lGGzHwTECBi96SPaLRf5FCIk0yB2w==", "dependencies": ["svelte", "svelte-preprocess"]}, "@vitest/expect@3.2.4": {"integrity": "sha512-Io0yyORnB6sikFlt8QW5K7slY4OjqNX9jmJQ02QDda8lyM6B5oNgVWoSoKPac8/kgnCUzuHQKrSLtu/uOqqrig==", "dependencies": ["@types/chai", "@vitest/spy", "@vitest/utils", "chai", "tiny<PERSON>bow"]}, "@vitest/mocker@3.2.4_vite@7.1.1__sass@1.90.0__picomatch@4.0.3_sass@1.90.0": {"integrity": "sha512-46ryTE9RZO/rfDd7pEqFl7etuyzekzEhUbTW3BvmeO/BcCMEgq59BKhek3dXDWgAj4oMK6OZi+vRr1wPW6qjEQ==", "dependencies": ["@vitest/spy", "estree-walker", "magic-string", "vite"], "optionalPeers": ["vite"]}, "@vitest/pretty-format@3.2.4": {"integrity": "sha512-IVNZik8IVRJRTr9fxlitMKeJeXFFFN0JaB9PHPGQ8NKQbGpfjlTx9zO4RefN8gp7eqjNy8nyK3NZmBzOPeIxtA==", "dependencies": ["tiny<PERSON>bow"]}, "@vitest/runner@3.2.4": {"integrity": "sha512-oukfKT9Mk41LreEW09vt45f8wx7DordoWUZMYdY/cyAk7w5TWkTRCNZYF7sX7n2wB7jyGAl74OxgwhPgKaqDMQ==", "dependencies": ["@vitest/utils", "pathe", "strip-literal"]}, "@vitest/snapshot@3.2.4": {"integrity": "sha512-dEYtS7qQP2CjU27QBC5oUOxLE/v5eLkGqPE0ZKEIDGMs4vKWe7IjgLOeauHsR0D5YuuycGRO5oSRXnwnmA78fQ==", "dependencies": ["@vitest/pretty-format", "magic-string", "pathe"]}, "@vitest/spy@3.2.4": {"integrity": "sha512-vAfasCOe6AIK70iP5UD11Ac4siNUNJ9i/9PZ3NKx07sG6sUxeag1LWdNrMWeKKYBLlzuK+Gn65Yd5nyL6ds+nw==", "dependencies": ["tiny<PERSON>y"]}, "@vitest/utils@3.2.4": {"integrity": "sha512-fB2V0JFrQSMsCo9HiSq3Ezpdv4iYaXRG1Sx8edX3MwxfyNn83mKiGzOcH+Fkxt4MHxr3y42fQi1oeAInqgX2QA==", "dependencies": ["@vitest/pretty-format", "loupe", "tiny<PERSON>bow"]}, "@webgpu/types@0.1.64": {"integrity": "sha512-84kRIAGV46LJTlJZWxShiOrNL30A+9KokD7RB3dRCIqODFjodS5tCD5yyiZ8kIReGVZSDfA3XkkwyyOIF6K62A=="}, "acorn@8.15.0": {"integrity": "sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==", "bin": true}, "agent-base@7.1.4": {"integrity": "sha512-MnA+YT8fwfJPgBx3m60MNqakm30XOkyIoH1y6huTQvC0PwZG7ki8NacLBcrPbNoo8vEZy7Jpuk7+jMO+CUovTQ=="}, "ansi-regex@5.0.1": {"integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="}, "ansi-styles@5.2.0": {"integrity": "sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA=="}, "aria-query@5.3.0": {"integrity": "sha512-b0P0sZPKtyu8HkeRAfCq0IfURZK+SuwMjY1UXGBU27wpAiTwQAIlq56IbIO+ytk/JjS1fMR14ee5WBBfKi5J6A==", "dependencies": ["dequal"]}, "aria-query@5.3.2": {"integrity": "sha512-COROpnaoap1E2F000S62r6A60uHZnmlvomhfyT2DlTcrY1OrBKn2UhH7qn5wTC9zMvD0AY7csdPSNwKP+7WiQw=="}, "assertion-error@2.0.1": {"integrity": "sha512-Izi8RQcffqCeNVgFigKli1ssklIbpHnCYc6AknXGYoB6grJqyeby7jv12JUQgmTAnIDnbck1uxksT4dzN3PWBA=="}, "axobject-query@4.1.0": {"integrity": "sha512-qIj0G9wZbMGNLjLmg1PT6v2mE9AH2zlnADJD/2tC6E00hgmhUOfEB6greHPAfLRSufHqROIUTkw6E+M3lH0PTQ=="}, "bidi-js@1.0.3": {"integrity": "sha512-RKshQI1R3YQ+n9YJz2QQ147P66ELpa1FQEg20Dk8oW9t2KgLbpDLLp9aGZ7y8WHSshDknG0bknqGw5/tyCs5tw==", "dependencies": ["require-from-string"]}, "braces@3.0.3": {"integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "dependencies": ["fill-range"]}, "buffer-from@1.1.2": {"integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ=="}, "cac@6.7.14": {"integrity": "sha512-b6<PERSON>lus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ=="}, "camera-controls@2.10.1_three@0.179.1": {"integrity": "sha512-KnaKdcvkBJ1Irbrzl8XD6WtZltkRjp869Jx8c0ujs9K+9WD+1D7ryBsCiVqJYUqt6i/HR5FxT7RLASieUD+Q5w==", "dependencies": ["three"]}, "chai@5.2.1": {"integrity": "sha512-5nFxhUrX0PqtyogoYOA8IPswy5sZFTOsBFl/9bNsmDLgsxYTzSZQJDPppDnZPTQbzSEm0hqGjWPzRemQCYbD6A==", "dependencies": ["assertion-error", "check-error", "deep-eql", "loupe", "pathval"]}, "check-error@2.1.1": {"integrity": "sha512-OAlb+T7V4Op9OwdkjmguYRqncdlx5JiofwOAUkmTF+jNdHwzTaTs4sRAGpzLF3oOz5xAyDGrPgeIDFQmDOTiJw=="}, "chokidar@4.0.3": {"integrity": "sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==", "dependencies": ["readdirp"]}, "clsx@2.1.1": {"integrity": "sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA=="}, "cookie@0.6.0": {"integrity": "sha512-U71cyTamuh1CRNCfpGY6to28lxvNwPG4Guz/EVjgf3Jmzv0vlDp1atT9eS5dDjMYHucpHbWns6Lwf3BKz6svdw=="}, "css.escape@1.5.1": {"integrity": "sha512-YUifsXXuknHlUsmlgyY0PKzgPOr7/FjCePfHNt0jxm83wHZi44VDMQ7/fGNkjY3/jV1MC+1CmZbaHzugyeRtpg=="}, "cssstyle@4.6.0": {"integrity": "sha512-2z+rWdzbbSZv6/rhtvzvqeZQHrBaqgogqt85sqFNbabZOuFbCVFb8kPeEtZjiKkbrm395irpNKiYeFeLiQnFPg==", "dependencies": ["@asamuzakjp/css-color", "rrweb-cssom"]}, "data-urls@5.0.0": {"integrity": "sha512-ZYP5VBHshaDAiVZxjbRVcFJpc+4xGgT0bK3vzy1HLN8jTO975HEbuYzZJcHoQEY5K1a0z8YayJkyVETa08eNTg==", "dependencies": ["whatwg-mimetype", "whatwg-url@14.2.0"]}, "debug@4.4.1": {"integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "dependencies": ["ms"]}, "decimal.js@10.6.0": {"integrity": "sha512-YpgQiITW3JXGntzdUmyUR1V812Hn8T1YVXhCu+wO3OpS4eU9l4YdD3qjyiKdV6mvV29zapkMeD390UVEf2lkUg=="}, "deep-eql@5.0.2": {"integrity": "sha512-h5k/5U50IJJFpzfL6nO9jaaumfjO/f2NjK/oYB2Djzm4p9L+3T9qWpZqZ2hAbLPuuYq9wrU08WQyBTL5GbPk5Q=="}, "deepmerge@4.3.1": {"integrity": "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A=="}, "dequal@2.0.3": {"integrity": "sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA=="}, "detect-libc@1.0.3": {"integrity": "sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==", "bin": true}, "devalue@5.1.1": {"integrity": "sha512-maua5KUiapvEwiEAe+XnlZ3Rh0GD+qI1J/nb9vrJc3muPXvcF/8gXYTWF76+5DAqHyDUtOIImEuo0YKE9mshVw=="}, "diet-sprite@0.0.1": {"integrity": "sha512-zSHI2WDAn1wJqJYxcmjWfJv3Iw8oL9reQIbEyx2x2/EZ4/qmUTIo8/5qOCurnAcq61EwtJJaZ0XTy2NRYqpB5A=="}, "dom-accessibility-api@0.5.16": {"integrity": "sha512-X7BJ2yElsnOJ30pZF4uIIDfBEVgF4XEBxL9Bxhy6dnrm5hkzqmsWHGTiHqRiITNhMyFLyAiWndIJP7Z1NTteDg=="}, "dom-accessibility-api@0.6.3": {"integrity": "sha512-7ZgogeTnjuHbo+ct10G9Ffp0mif17idi0IyWNVA/wcwcm7NPOD/WEHVP3n7n3MhXqxoIYm8d6MuZohYWIZ4T3w=="}, "drizzle-kit@0.31.4_esbuild@0.25.8": {"integrity": "sha512-tCPWVZWZqWVx2XUsVpJRnH9Mx0ClVOf5YUHerZ5so1OKSlqww4zy1R5ksEdGRcO3tM3zj0PYN6V48TbQCL1RfA==", "dependencies": ["@drizzle-team/brocli", "@esbuild-kit/esm-loader", "esbuild@0.25.8", "esbuild-register"], "bin": true}, "drizzle-orm@0.44.4_postgres@3.4.7": {"integrity": "sha512-ZyzKFpTC/Ut3fIqc2c0dPZ6nhchQXriTsqTNs4ayRgl6sZcFlMs9QZKPSHXK4bdOf41GHGWf+FrpcDDYwW+W6Q==", "dependencies": ["postgres"], "optionalPeers": ["postgres"]}, "earcut@2.2.4": {"integrity": "sha512-/pjZsA1b4RPHbeWZQn66SWS8nZZWLQQ23oE3Eam7aroEFGEvwKAsJfZ9ytiEMycfzXWpca4FA9QIOehf7PocBQ=="}, "entities@6.0.1": {"integrity": "sha512-aN97NXWF6AWBTahfVOIrB/NShkzi5H7F9r1s9mD3cDj4Ko5f2qhhVoYMibXF7GlLveb/D2ioWay8lxI97Ven3g=="}, "es-module-lexer@1.7.0": {"integrity": "sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA=="}, "esbuild-register@3.6.0_esbuild@0.25.8": {"integrity": "sha512-H2/S7Pm8a9CL1uhp9OvjwrBh5Pvx0H8qVOxNu8Wed9Y7qv56MPtq+GGM8RJpq6glYJn9Wspr8uw7l55uyinNeg==", "dependencies": ["debug", "esbuild@0.25.8"]}, "esbuild@0.18.20": {"integrity": "sha512-ceqxoedUrcayh7Y7ZX6NdbbDzGROiyVBgC4PriJThBKSVPWnnFHZAkfI1lJT8QFkOwH4qOS2SJkS4wvpGl8BpA==", "optionalDependencies": ["@esbuild/android-arm@0.18.20", "@esbuild/android-arm64@0.18.20", "@esbuild/android-x64@0.18.20", "@esbuild/darwin-arm64@0.18.20", "@esbuild/darwin-x64@0.18.20", "@esbuild/freebsd-arm64@0.18.20", "@esbuild/freebsd-x64@0.18.20", "@esbuild/linux-arm@0.18.20", "@esbuild/linux-arm64@0.18.20", "@esbuild/linux-ia32@0.18.20", "@esbuild/linux-loong64@0.18.20", "@esbuild/linux-mips64el@0.18.20", "@esbuild/linux-ppc64@0.18.20", "@esbuild/linux-riscv64@0.18.20", "@esbuild/linux-s390x@0.18.20", "@esbuild/linux-x64@0.18.20", "@esbuild/netbsd-x64@0.18.20", "@esbuild/openbsd-x64@0.18.20", "@esbuild/sunos-x64@0.18.20", "@esbuild/win32-arm64@0.18.20", "@esbuild/win32-ia32@0.18.20", "@esbuild/win32-x64@0.18.20"], "scripts": true, "bin": true}, "esbuild@0.25.8": {"integrity": "sha512-vVC0USHGtMi8+R4Kz8rt6JhEWLxsv9Rnu/lGYbPR8u47B+DCBksq9JarW0zOO7bs37hyOK1l2/oqtbciutL5+Q==", "optionalDependencies": ["@esbuild/aix-ppc64", "@esbuild/android-arm@0.25.8", "@esbuild/android-arm64@0.25.8", "@esbuild/android-x64@0.25.8", "@esbuild/darwin-arm64@0.25.8", "@esbuild/darwin-x64@0.25.8", "@esbuild/freebsd-arm64@0.25.8", "@esbuild/freebsd-x64@0.25.8", "@esbuild/linux-arm@0.25.8", "@esbuild/linux-arm64@0.25.8", "@esbuild/linux-ia32@0.25.8", "@esbuild/linux-loong64@0.25.8", "@esbuild/linux-mips64el@0.25.8", "@esbuild/linux-ppc64@0.25.8", "@esbuild/linux-riscv64@0.25.8", "@esbuild/linux-s390x@0.25.8", "@esbuild/linux-x64@0.25.8", "@esbuild/netbsd-arm64", "@esbuild/netbsd-x64@0.25.8", "@esbuild/openbsd-arm64", "@esbuild/openbsd-x64@0.25.8", "@esbuild/openharmony-arm64", "@esbuild/sunos-x64@0.25.8", "@esbuild/win32-arm64@0.25.8", "@esbuild/win32-ia32@0.25.8", "@esbuild/win32-x64@0.25.8"], "scripts": true, "bin": true}, "esm-env@1.2.2": {"integrity": "sha512-Epxrv+Nr/CaL4ZcFGPJIYLWFom+YeV1DqMLHJoEd9SYRxNbaFruBwfEX/kkHUJf55j2+TUbmDcmuilbP1TmXHA=="}, "esrap@2.1.0": {"integrity": "sha512-yzmPNpl7TBbMRC5Lj2JlJZNPml0tzqoqP5B1JXycNUwtqma9AKCO0M2wHrdgsHcy1WRW7S9rJknAMtByg3usgA==", "dependencies": ["@jridgewell/sourcemap-codec"]}, "estree-walker@3.0.3": {"integrity": "sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==", "dependencies": ["@types/estree"]}, "expect-type@1.2.2": {"integrity": "sha512-JhFGDVJ7tmDJItKhYgJCGLOWjuK9vPxiXoUFLwLDc99NlmklilbiQJwoctZtt13+xMw91MCk/REan6MWHqDjyA=="}, "fdir@6.4.6_picomatch@4.0.3": {"integrity": "sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==", "dependencies": ["picomatch@4.0.3"], "optionalPeers": ["picomatch@4.0.3"]}, "fflate@0.8.2": {"integrity": "sha512-cPJU47OaAoCbg0pBvzsgpTPhmhqI5eJjh/JIu8tPj5q+T7iLvW/JAYUqmE7KOB4R1ZyEhzBaIQpQpardBF5z8A=="}, "fill-range@7.1.1": {"integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "dependencies": ["to-regex-range"]}, "fsevents@2.3.2": {"integrity": "sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==", "os": ["darwin"], "scripts": true}, "fsevents@2.3.3": {"integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "os": ["darwin"], "scripts": true}, "get-tsconfig@4.10.1": {"integrity": "sha512-auHyJ4AgMz7vgS8Hp3N6HXSmlMdUyhSUrfBF16w153rxtLIEOE+HGqaBppczZvnHLqQJfiHotCYpNhl0lUROFQ==", "dependencies": ["resolve-pkg-maps"]}, "html-encoding-sniffer@4.0.0": {"integrity": "sha512-Y22oTqIU4uuPgEemfz7NDJz6OeKf12Lsu+QC+s3BVpda64lTiMYCyGwg5ki4vFxkMwQdeZDl2adZoqUgdFuTgQ==", "dependencies": ["whatwg-encoding"]}, "http-proxy-agent@7.0.2": {"integrity": "sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==", "dependencies": ["agent-base", "debug"]}, "https-proxy-agent@7.0.6": {"integrity": "sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==", "dependencies": ["agent-base", "debug"]}, "iconv-lite@0.6.3": {"integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "dependencies": ["safer-buffer"]}, "immutable@5.1.3": {"integrity": "sha512-+chQdDfvscSF1SJqv2gn4SRO2ZyS3xL3r7IW/wWEEzrzLisnOlKiQu5ytC/BVNcS15C39WT2Hg/bjKjDMcu+zg=="}, "indent-string@4.0.0": {"integrity": "sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg=="}, "is-extglob@2.1.1": {"integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="}, "is-glob@4.0.3": {"integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "dependencies": ["is-extglob"]}, "is-number@7.0.0": {"integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="}, "is-potential-custom-element-name@1.0.1": {"integrity": "sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ=="}, "is-reference@3.0.3": {"integrity": "sha512-ixkJoqQvAP88E6wLydLGGqCJsrFUnqoH6HnaczB8XmDH1oaWU+xxdptvikTgaEhtZ53Ky6YXiBuUI2WXLMCwjw==", "dependencies": ["@types/estree"]}, "js-tokens@4.0.0": {"integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="}, "js-tokens@9.0.1": {"integrity": "sha512-mxa9E9ITFOt0ban3j6L5MpjwegGz6lBQmM1IJkWeBZGcMxto50+eWdjC/52xDbS2vy0k7vIMK0Fe2wfL9OQSpQ=="}, "jsdom@26.1.0": {"integrity": "sha512-Cvc9WUhxSMEo4McES3P7oK3QaXldCfNWp7pl2NNeiIFlCoLr3kfq9kb1fxftiwk1FLV7CvpvDfonxtzUDeSOPg==", "dependencies": ["cssstyle", "data-urls", "decimal.js", "html-encoding-sniffer", "http-proxy-agent", "https-proxy-agent", "is-potential-custom-element-name", "nwsapi", "parse5", "rrweb-cssom", "saxes", "symbol-tree", "tough-cookie", "w3c-xmlserializer", "webidl-conversions@7.0.0", "whatwg-encoding", "whatwg-mimetype", "whatwg-url@14.2.0", "ws", "xml-name-validator"]}, "kleur@4.1.5": {"integrity": "sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ=="}, "locate-character@3.0.0": {"integrity": "sha512-SW13ws7BjaeJ6p7Q6CO2nchbYEc3X3J6WrmTTDto7yMPqVSZTUyY5Tjbid+Ab8gLnATtygYtiDIJGQRRn2ZOiA=="}, "lodash@4.17.21": {"integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="}, "loupe@3.2.0": {"integrity": "sha512-2NCfZcT5VGVNX9mSZIxLRkEAegDGBpuQZBy13desuHeVORmBDyAET4TkJr4SjqQy3A8JDofMN6LpkK8Xcm/dlw=="}, "lru-cache@10.4.3": {"integrity": "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ=="}, "lz-string@1.5.0": {"integrity": "sha512-h5bgJWpxJNswbU7qCrV0tIKQCaS3blPDrqKWx+QxzuzL1zGUzij9XCWLrSLsJPu5t+eWA/ycetzYAO5IOMcWAQ==", "bin": true}, "maath@0.10.8_@types+three@0.179.0_three@0.179.1": {"integrity": "sha512-tRvbDF0Pgqz+9XUa4jjfgAQ8/aPKmQdWXilFu2tMy4GWj4NOsx99HlULO4IeREfbO3a0sA145DZYyvXPkybm0g==", "dependencies": ["@types/three", "three"]}, "magic-string@0.30.17": {"integrity": "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==", "dependencies": ["@jridgewell/sourcemap-codec"]}, "meshoptimizer@0.22.0": {"integrity": "sha512-IebiK79sqIy+E4EgOr+CAw+Ke8hAspXKzBd0JdgEmPHiAwmvEj2S4h1rfvo+o/BnfEYd/jAOg5IeeIjzlzSnDg=="}, "micromatch@4.0.8": {"integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "dependencies": ["braces", "picomatch@2.3.1"]}, "min-indent@1.0.1": {"integrity": "sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg=="}, "mitt@3.0.1": {"integrity": "sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw=="}, "mri@1.2.0": {"integrity": "sha512-tzzskb3bG8LvYGFF/mDTpq3jpI6Q9wc3LEmBaghu+DdCssd1FakN7Bc0hVNmEyGq1bq3RgfkCb3cmQLpNPOroA=="}, "mrmime@2.0.1": {"integrity": "sha512-Y3wQdFg2Va6etvQ5I82yUhGdsKrcYox6p7FfL1LbK2J4V01F9TGlepTIhnK24t7koZibmg82KGglhA1XK5IsLQ=="}, "ms@2.1.3": {"integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}, "nanoid@3.3.11": {"integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==", "bin": true}, "node-addon-api@7.1.1": {"integrity": "sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ=="}, "nwsapi@2.2.21": {"integrity": "sha512-o6nIY3qwiSXl7/LuOU0Dmuctd34Yay0yeuZRLFmDPrrdHpXKFndPj3hM+YEPVHYC5fx2otBx4Ilc/gyYSAUaIA=="}, "parse5@7.3.0": {"integrity": "sha512-IInvU7fabl34qmi9gY8XOVxhYyMyuH2xUNpb2q8/Y+7552KlejkRvqvD19nMoUW/uQGGbqNpA6Tufu5FL5BZgw==", "dependencies": ["entities"]}, "pathe@2.0.3": {"integrity": "sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w=="}, "pathval@2.0.1": {"integrity": "sha512-//nshmD55c46FuFw26xV/xFAaB5HF9Xdap7HJBBnrKdAd6/GxDBaNA1870O79+9ueg61cZLSVc+OaFlfmObYVQ=="}, "picocolors@1.1.1": {"integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA=="}, "picomatch@2.3.1": {"integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="}, "picomatch@4.0.3": {"integrity": "sha512-5gTmgEY/sqK6gFXLIsQNH19lWb4ebPDLA4SdLP7dsWkIXHWlG66oPuVvXSGFPppYZz8ZDZq0dYYrbHfBCVUb1Q=="}, "playwright-core@1.54.2": {"integrity": "sha512-n5r4HFbMmWsB4twG7tJLDN9gmBUeSPcsBZiWSE4DnYz9mJMAFqr2ID7+eGC9kpEnxExJ1epttwR59LEWCk8mtA==", "bin": true}, "playwright@1.54.2": {"integrity": "sha512-Hu/BMoA1NAdRUuulyvQC0pEqZ4vQbGfn8f7wPXcnqQmM+zct9UliKxsIkLNmz/ku7LElUNqmaiv1TG/aL5ACsw==", "dependencies": ["playwright-core"], "optionalDependencies": ["fsevents@2.3.2"], "bin": true}, "postcss@8.5.6": {"integrity": "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==", "dependencies": ["nanoid", "picocolors", "source-map-js"]}, "postgres@3.4.7": {"integrity": "sha512-Jtc2612XINuBjIl/QTWsV5UvE8UHuNblcO3vVADSrKsrc6RqGX6lOW1cEo3CM2v0XG4Nat8nI+YM7/f26VxXLw=="}, "pretty-format@27.5.1": {"integrity": "sha512-Qb1gy5OrP5+zDf2Bvnzdl3jsTf1qXVMazbvCoKhtKqVs4/YK4ozX4gKQJJVyNe+cajNPn0KoC0MC3FUmaHWEmQ==", "dependencies": ["ansi-regex", "ansi-styles", "react-is"]}, "punycode@2.3.1": {"integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg=="}, "react-is@17.0.2": {"integrity": "sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w=="}, "readdirp@4.1.2": {"integrity": "sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg=="}, "redent@3.0.0": {"integrity": "sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==", "dependencies": ["indent-string", "strip-indent"]}, "require-from-string@2.0.2": {"integrity": "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw=="}, "resolve-pkg-maps@1.0.0": {"integrity": "sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw=="}, "rollup@4.46.2": {"integrity": "sha512-WMmLFI+Boh6xbop+OAGo9cQ3OgX9MIg7xOQjn+pTCwOkk+FNDAeAemXkJ3HzDJrVXleLOFVa1ipuc1AmEx1Dwg==", "dependencies": ["@types/estree"], "optionalDependencies": ["@rollup/rollup-android-arm-eabi", "@rollup/rollup-android-arm64", "@rollup/rollup-darwin-arm64", "@rollup/rollup-darwin-x64", "@rollup/rollup-freebsd-arm64", "@rollup/rollup-freebsd-x64", "@rollup/rollup-linux-arm-gnueabihf", "@rollup/rollup-linux-arm-musleabihf", "@rollup/rollup-linux-arm64-gnu", "@rollup/rollup-linux-arm64-musl", "@rollup/rollup-linux-loongarch64-gnu", "@rollup/rollup-linux-ppc64-gnu", "@rollup/rollup-linux-riscv64-gnu", "@rollup/rollup-linux-riscv64-musl", "@rollup/rollup-linux-s390x-gnu", "@rollup/rollup-linux-x64-gnu", "@rollup/rollup-linux-x64-musl", "@rollup/rollup-win32-arm64-msvc", "@rollup/rollup-win32-ia32-msvc", "@rollup/rollup-win32-x64-msvc", "fsevents@2.3.3"], "bin": true}, "rrweb-cssom@0.8.0": {"integrity": "sha512-guoltQEx+9aMf2gDZ0s62EcV8lsXR+0w8915TC3ITdn2YueuNjdAYh/levpU9nFaoChh9RUS5ZdQMrKfVEN9tw=="}, "sade@1.8.1": {"integrity": "sha512-xal3CZX1Xlo/k4ApwCFrHVACi9fBqJ7V+mwhBsuf/1IOKbBy098Fex+Wa/5QMubw09pSZ/u8EY8PWgevJsXp1A==", "dependencies": ["mri"]}, "safer-buffer@2.1.2": {"integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}, "sass@1.90.0": {"integrity": "sha512-9GUyuksjw70uNpb1MTYWsH9MQHOHY6kwfnkafC24+7aOMZn9+rVMBxRbLvw756mrBFbIsFg6Xw9IkR2Fnn3k+Q==", "dependencies": ["chokidar", "immutable", "source-map-js"], "optionalDependencies": ["@parcel/watcher"], "bin": true}, "saxes@6.0.0": {"integrity": "sha512-xAg7SOnEhrm5zI3puOOKyy1OMcMlIJZYNJY7xLBwSze0UjhPLnWfj2GF2EpT0jmzaJKIWKHLsaSSajf35bcYnA==", "dependencies": ["xmlchars"]}, "set-cookie-parser@2.7.1": {"integrity": "sha512-IOc8uWeOZgnb3ptbCURJWNjWUPcO3ZnTTdzsurqERrP6nPyv+paC55vJM0LpOlT2ne+Ix+9+CRG1MNLlyZ4GjQ=="}, "siginfo@2.0.0": {"integrity": "sha512-ybx0WO1/8bSBLEWXZvEd7gMW3Sn3JFlW3TvX1nREbDLRNQNaeNN8WK0meBwPdAaOI7TtRRRJn/Es1zhrrCHu7g=="}, "sirv@3.0.1": {"integrity": "sha512-FoqMu0NCGBLCcAkS1qA+XJIQTR6/JHfQXl+uGteNCQ76T91DMUjPa9xfmeqMY3z80nLSg9yQmNjK0Px6RWsH/A==", "dependencies": ["@polka/url", "mrmime", "totalist"]}, "source-map-js@1.2.1": {"integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA=="}, "source-map-support@0.5.21": {"integrity": "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==", "dependencies": ["buffer-from", "source-map"]}, "source-map@0.6.1": {"integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="}, "stackback@0.0.2": {"integrity": "sha512-1XMJE5fQo1jGH6Y/7ebnwPOBEkIEnT4QF32d5R1+VXdXveM0IBMJt8zfaxX1P3QhVwrYe+576+jkANtSS2mBbw=="}, "std-env@3.9.0": {"integrity": "sha512-UGvjygr6F6tpH7o2qyqR6QYpwraIjKSdtzyBdyytFOHmPZY917kwdwLG0RbOjWOnKmnm3PeHjaoLLMie7kPLQw=="}, "strip-indent@3.0.0": {"integrity": "sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==", "dependencies": ["min-indent"]}, "strip-literal@3.0.0": {"integrity": "sha512-TcccoMhJOM3OebGhSBEmp3UZ2SfDMZUEBdRA/9ynfLi8yYajyWX3JiXArcJt4Umh4vISpspkQIY8ZZoCqjbviA==", "dependencies": ["js-tokens@9.0.1"]}, "svelte-check@4.3.1_svelte@5.38.0__acorn@8.15.0_typescript@5.9.2": {"integrity": "sha512-lkh8gff5gpHLjxIV+IaApMxQhTGnir2pNUAqcNgeKkvK5bT/30Ey/nzBxNLDlkztCH4dP7PixkMt9SWEKFPBWg==", "dependencies": ["@jridgewell/trace-mapping", "chokidar", "fdir", "picocolors", "sade", "svelte", "typescript"], "bin": true}, "svelte-preprocess@6.0.3_sass@1.90.0_svelte@5.38.0__acorn@8.15.0_typescript@5.9.2": {"integrity": "sha512-PLG2k05qHdhmRG7zR/dyo5qKvakhm8IJ+hD2eFRQmMLHp7X3eJnjeupUtvuRpbNiF31RjVw45W+abDwHEmP5OA==", "dependencies": ["sass", "svelte", "typescript"], "optionalPeers": ["sass", "typescript"], "scripts": true}, "svelte@5.38.0_acorn@8.15.0": {"integrity": "sha512-cWF1Oc2IM/QbktdK89u5lt9MdKxRtQnRKnf2tq6KOhYuhLOd2hbMuTiJ+vWMzAeMDe81AzbCgLd4GVtOJ4fDRg==", "dependencies": ["@ampproject/remapping", "@jridgewell/sourcemap-codec", "@sveltejs/acorn-typescript", "@types/estree", "acorn", "aria-query@5.3.2", "axobject-query", "clsx", "esm-env", "esrap", "is-reference", "locate-character", "magic-string", "zimmerframe"]}, "symbol-tree@3.2.4": {"integrity": "sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw=="}, "three-instanced-uniforms-mesh@0.52.4_three@0.179.1": {"integrity": "sha512-YwDBy05hfKZQtU+Rp0KyDf9yH4GxfhxMbVt9OYruxdgLfPwmDG5oAbGoW0DrKtKZSM3BfFcCiejiOHCjFBTeng==", "dependencies": ["three", "troika-three-utils"]}, "three-mesh-bvh@0.9.1_three@0.179.1": {"integrity": "sha512-WNT+m9jGQgtp4YdtwEnl4oFylNVifRf7iphlwWdJ4bJu7oNkY0xHIyntep9OzHuR1hpe/pyAP840gB/EsYDJfg==", "dependencies": ["three"]}, "three-perf@1.0.11_three@0.179.1": {"integrity": "sha512-OgBpZjwL+csQKGKZjpkH/QHdbGFMxqngMbSEJeSnVNfXDYd6On7WHNv/GhUZH4YxIpNMwMahBWrNnsJvnbSJHQ==", "dependencies": ["three", "troika-three-text", "tweakpane"]}, "three-viewport-gizmo@2.2.0_three@0.179.1": {"integrity": "sha512-Jo9Liur1rUmdKk75FZumLU/+hbF+RtJHi1qsKZpntjKlCYScK6tjbYoqvJ9M+IJphrlQJF5oReFW7Sambh0N4Q==", "dependencies": ["three"]}, "three@0.179.1": {"integrity": "sha512-5y/elSIQbrvKOISxpwXCR4sQqHtGiOI+MKLc3SsBdDXA2hz3Mdp3X59aUp8DyybMa34aeBwbFTpdoLJaUDEWSw=="}, "tinybench@2.9.0": {"integrity": "sha512-0+DUvqWMValLmha6lr4kD8iAMK1HzV0/aKnCtWb9v9641TnP/MFb7Pc2bxoxQjTXAErryXVgUOfv2YqNllqGeg=="}, "tinyexec@0.3.2": {"integrity": "sha512-KQQR9yN7R5+OSwaK0XQoj22pwHoTlgYqmUscPYoknOoWCWfj/5/ABTMRi69FrKU5ffPVh5QcFikpWJI/P1ocHA=="}, "tinyglobby@0.2.14_picomatch@4.0.3": {"integrity": "sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==", "dependencies": ["fdir", "picomatch@4.0.3"]}, "tinypool@1.1.1": {"integrity": "sha512-Zba82s87IFq9A9XmjiX5uZA/ARWDrB03OHlq+Vw1fSdt0I+4/Kutwy8BP4Y/y/aORMo61FQ0vIb5j44vSo5Pkg=="}, "tinyrainbow@2.0.0": {"integrity": "sha512-op4nsTR47R6p0vMUUoYl/a+ljLFVtlfaXkLQmqfLR1qHma1h/ysYk4hEXZ880bf2CYgTskvTa/e196Vd5dDQXw=="}, "tinyspy@4.0.3": {"integrity": "sha512-t2T/WLB2WRgZ9EpE4jgPJ9w+i66UZfDc8wHh0xrwiRNN+UwH98GIJkTeZqX9rg0i0ptwzqW+uYeIF0T4F8LR7A=="}, "tldts-core@6.1.86": {"integrity": "sha512-Je6p7pkk+KMzMv2XXKmAE3McmolOQFdxkKw0R8EYNr7sELW46JqnNeTX8ybPiQgvg1ymCoF8LXs5fzFaZvJPTA=="}, "tldts@6.1.86": {"integrity": "sha512-WMi/OQ2axVTf/ykqCQgXiIct+mSQDFdH2fkwhPwgEwvJ1kSzZRiinb0zF2Xb8u4+OqPChmyI6MEu4EezNJz+FQ==", "dependencies": ["tldts-core"], "bin": true}, "to-regex-range@5.0.1": {"integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "dependencies": ["is-number"]}, "totalist@3.0.1": {"integrity": "sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ=="}, "tough-cookie@5.1.2": {"integrity": "sha512-FVDYdxtnj0G6Qm/DhNPSb8Ju59ULcup3tuJxkFb5K8Bv2pUXILbf0xZWU8PX8Ov19OXljbUyveOFwRMwkXzO+A==", "dependencies": ["tldts"]}, "tr46@0.0.3": {"integrity": "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw=="}, "tr46@5.1.1": {"integrity": "sha512-hdF5ZgjTqgAntKkklYw0R03MG2x/bSzTtkxmIRw/sTNV8YXsCJ1tfLAX23lhxhHJlEf3CRCOCGGWw3vI3GaSPw==", "dependencies": ["punycode"]}, "troika-three-text@0.52.4_three@0.179.1": {"integrity": "sha512-V50EwcYGruV5rUZ9F4aNsrytGdKcXKALjEtQXIOBfhVoZU9VAqZNIoGQ3TMiooVqFAbR1w15T+f+8gkzoFzawg==", "dependencies": ["bidi-js", "three", "troika-three-utils", "troika-worker-utils", "webgl-sdf-generator"]}, "troika-three-utils@0.52.4_three@0.179.1": {"integrity": "sha512-NORAStSVa/BDiG52Mfudk4j1FG4jC4ILutB3foPnfGbOeIs9+G5vZLa0pnmnaftZUGm4UwSoqEpWdqvC7zms3A==", "dependencies": ["three"]}, "troika-worker-utils@0.52.0": {"integrity": "sha512-W1CpvTHykaPH5brv5VHLfQo9D1OYuo0cSBEUQFFT/nBUzM8iD6Lq2/tgG/f1OelbAS1WtaTPQzE5uM49egnngw=="}, "tweakpane@3.1.10": {"integrity": "sha512-rqwnl/pUa7+inhI2E9ayGTqqP0EPOOn/wVvSWjZsRbZUItzNShny7pzwL3hVlaN4m9t/aZhsP0aFQ9U5VVR2VQ=="}, "typescript@5.9.2": {"integrity": "sha512-CWBzXQrc/qOkhidw1OzBTQuYRbfyxDXJMVJ1XNwUHGROVmuaeiEm3OslpZ1RV96d7SKKjZKrSJu3+t/xlw3R9A==", "bin": true}, "undici-types@6.21.0": {"integrity": "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ=="}, "vite-node@3.2.4_sass@1.90.0": {"integrity": "sha512-EbKSKh+bh1E1IFxeO0pg1n4dvoOTt0UDiXMd/qn++r98+jPO1xtJilvXldeuQ8giIB5IkpjCgMleHMNEsGH6pg==", "dependencies": ["cac", "debug", "es-module-lexer", "pathe", "vite"], "bin": true}, "vite@7.1.1_sass@1.90.0_picomatch@4.0.3": {"integrity": "sha512-yJ+Mp7OyV+4S+afWo+QyoL9jFWD11QFH0i5i7JypnfTcA1rmgxCbiA8WwAICDEtZ1Z1hzrVhN8R8rGTqkTY8ZQ==", "dependencies": ["esbuild@0.25.8", "fdir", "picomatch@4.0.3", "postcss", "rollup", "sass", "tinyglobby"], "optionalDependencies": ["fsevents@2.3.3"], "optionalPeers": ["sass"], "bin": true}, "vitefu@1.1.1_vite@7.1.1__sass@1.90.0__picomatch@4.0.3_sass@1.90.0": {"integrity": "sha512-B/Fegf3i8zh0yFbpzZ21amWzHmuNlLlmJT6n7bu5e+pCHUKQIfXSYokrqOBGEMMe9UG2sostKQF9mml/vYaWJQ==", "dependencies": ["vite"], "optionalPeers": ["vite"]}, "vitest@3.2.4_jsdom@26.1.0_vite@7.1.1__sass@1.90.0__picomatch@4.0.3_sass@1.90.0": {"integrity": "sha512-LUCP5ev3GURDysTWiP47wRRUpLKMOfPh+yKTx3kVIEiu5KOMeqzpnYNsKyOoVrULivR8tLcks4+lga33Whn90A==", "dependencies": ["@types/chai", "@vitest/expect", "@vitest/mocker", "@vitest/pretty-format", "@vitest/runner", "@vitest/snapshot", "@vitest/spy", "@vitest/utils", "chai", "debug", "expect-type", "jsdom", "magic-string", "pathe", "picomatch@4.0.3", "std-env", "tinybench", "tinyexec", "tinyglobby", "tinypool", "tiny<PERSON>bow", "vite", "vite-node", "why-is-node-running"], "optionalPeers": ["jsdom"], "bin": true}, "w3c-xmlserializer@5.0.0": {"integrity": "sha512-o8qghlI8NZHU1lLPrpi2+Uq7abh4GGPpYANlalzWxyWteJOCsr/P+oPBA49TOLu5FTZO4d3F9MnWJfiMo4BkmA==", "dependencies": ["xml-name-validator"]}, "webgl-sdf-generator@1.1.1": {"integrity": "sha512-9Z0JcMTFxeE+b2x1LJTdnaT8rT8aEp7MVxkNwoycNmJWwPdzoXzMh0BjJSh/AEFP+KPYZUli814h8bJZFIZ2jA=="}, "webidl-conversions@3.0.1": {"integrity": "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ=="}, "webidl-conversions@7.0.0": {"integrity": "sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g=="}, "whatwg-encoding@3.1.1": {"integrity": "sha512-6qN4hJdMwfYBtE3YBTTHhoeuUrDBPZmbQaxWAqSALV/MeEnR5z1xd8UKud2RAkFoPkmB+hli1TZSnyi84xz1vQ==", "dependencies": ["iconv-lite"]}, "whatwg-mimetype@4.0.0": {"integrity": "sha512-QaKxh0eNIi2mE9p2vEdzfagOKHCcj1pJ56EEHGQOVxp8r9/iszLUUV7v89x9O1p/T+NlTM5W7jW6+cz4Fq1YVg=="}, "whatwg-url@14.2.0": {"integrity": "sha512-De72GdQZzNTUBBChsXueQUnPKDkg/5A5zp7pFDuQAj5UFoENpiACU0wlCvzpAGnTkj++ihpKwKyYewn/XNUbKw==", "dependencies": ["tr46@5.1.1", "webidl-conversions@7.0.0"]}, "whatwg-url@5.0.0": {"integrity": "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==", "dependencies": ["tr46@0.0.3", "webidl-conversions@3.0.1"]}, "why-is-node-running@2.3.0": {"integrity": "sha512-hUrmaWBdVDcxvYqnyh09zunKzROWjbZTiNy8dBEjkS7ehEDQibXJ7XvlmtbwuTclUiIyN+CyXQD4Vmko8fNm8w==", "dependencies": ["siginfo", "stackback"], "bin": true}, "ws@8.18.3": {"integrity": "sha512-PEIGCY5tSlUt50cqyMXfCzX+oOPqN0vuGqWzbcJ2xvnkzkq46oOpz7dQaTDBdfICb4N14+GARUDw2XV2N4tvzg=="}, "xml-name-validator@5.0.0": {"integrity": "sha512-EvGK8EJ3DhaHfbRlETOWAS5pO9MZITeauHKJyb8wyajUfQUenkIg2MvLDTZ4T/TgIcm3HU0TFBgWWboAZ30UHg=="}, "xmlchars@2.2.0": {"integrity": "sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw=="}, "zimmerframe@1.1.2": {"integrity": "sha512-rAbqEGa8ovJy4pyBxZM70hg4pE6gDgaQ0Sl9M3enG3I0d6H4XSAM3GeNGLKnsBpuijUow064sf7ww1nutC5/3w=="}}, "workspace": {"packageJson": {"dependencies": ["npm:@playwright/test@^1.54.2", "npm:@supabase/supabase-js@^2.54.0", "npm:@sveltejs/adapter-auto@^6.0.2", "npm:@sveltejs/kit@^2.27.3", "npm:@sveltejs/vite-plugin-svelte@^6.1.1", "npm:@testing-library/jest-dom@^6.6.4", "npm:@testing-library/svelte@^5.2.8", "npm:@threlte/core@^8.1.4", "npm:@threlte/extras@^9.4.4", "npm:@types/three@0.179", "npm:@vaie/hui@^0.0.0", "npm:drizzle-kit@~0.31.4", "npm:drizzle-orm@~0.44.4", "npm:jsdom@26", "npm:postgres@^3.4.5", "npm:sass@^1.90.0", "npm:svelte-check@^4.3.1", "npm:svelte@^5.38.0", "npm:three@~0.179.1", "npm:typescript@^5.9.2", "npm:vite@^7.1.1", "npm:vitest@^3.2.4"]}}}