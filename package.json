{"name": "height-chart", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "test:e2e": "playwright test", "test": "npm run test:e2e && npm run test:unit -- --run", "test:unit": "vitest"}, "devDependencies": {"@playwright/test": "^1.49.1", "@sveltejs/kit": "^2.27.3", "@sveltejs/vite-plugin-svelte": "^6.1.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/svelte": "^5.2.8", "@types/three": "^0.179.0", "drizzle-kit": "^0.31.4", "happy-dom": "^18.0.1", "jest-canvas-mock": "^2.5.2", "jsdom": "^26.0.0", "svelte": "^5.38.0", "svelte-check": "^4.3.1", "typescript": "^5.9.2", "vite": "^7.1.1", "vitest": "^3.2.4"}, "dependencies": {"@playwright/test": "^1.54.2", "@supabase/supabase-js": "^2.54.0", "@sveltejs/adapter-auto": "^6.0.2", "@threlte/core": "^8.1.4", "@threlte/extras": "^9.4.4", "@vaie/hui": "^0.0.0", "drizzle-orm": "^0.44.4", "postgres": "^3.4.5", "sass": "^1.90.0", "three": "^0.179.1"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}