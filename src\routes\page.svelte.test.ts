import { describe, test, expect } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { render, screen } from '@testing-library/svelte';
import Page from './+page.svelte';

describe('/+page.svelte', () => {
  test('renders add character button and canvas', () => {
    render(Page);
    expect(screen.getByRole('button', { name: /add a character/i })).toBeInTheDocument();
  });

  test("renders canvas", () => {
    const { container } = render(Page);
    expect(container.querySelector('canvas')).toBeInTheDocument();
  });
});
