import { describe, test, expect, vi, beforeEach } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { render, screen } from '@testing-library/svelte';
import Page from './+page.svelte';

// Mock the fetch function to prevent API calls during tests
beforeEach(() => {
  vi.stubGlobal('fetch', vi.fn(async () => new Response(JSON.stringify([]), { status: 200 })));
});

describe('/+page.svelte', () => {
  test('renders add character button and canvas', () => {
    render(Page);
    expect(screen.getByRole('button', { name: /add a character/i })).toBeInTheDocument();
  });

  test("renders canvas", () => {
    const { container } = render(Page);
    expect(container.querySelector('canvas')).toBeInTheDocument();
  });
});
